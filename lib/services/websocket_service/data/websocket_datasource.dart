import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/device_info/i_device_info_service.dart';
import 'package:flutter_audio_room/services/package_info/i_package_info_service.dart';
import 'package:flutter_audio_room/services/token_refresh/token_refresh_service.dart';
import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';
import 'package:flutter_audio_room/shared/data/consts/sp_keys.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/models/response.dart'
    as response;
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

enum CloseCode {
  initial(0),
  normal(1),
  goingAway(1000),
  networkError(1005),
  ;

  const CloseCode(this.value);
  final int value;
}

/// WebSocket data source interface
abstract class WebSocketDataSource {
  /// 获取WebSocket URL
  String get url;

  /// 连接到WebSocket服务器
  Future<ResultWithData<void>> connect();

  /// 断开WebSocket连接
  Future<ResultWithData<void>> disconnect({required CloseCode closeCode});

  /// 消息流，用于接收服务器消息
  Stream<response.WebsocketResponse> get messageStream;

  /// 发送消息到WebSocket服务器
  Future<ResultWithData<void>> sendMessage(WebSocketMessageEntity message);

  /// 当前连接状态
  bool get isConnected;

  /// 开始心跳检测
  void startHeartbeat();

  /// 停止心跳检测
  void stopHeartbeat();

  /// 尝试刷新Token并重连
  Future<void> tryRefreshTokenAndReconnect();

  /// 重置重连状态，允许重新开始重连
  void resetReconnectionState();

  Future<void> dispose();
}

/// WebSocket data source implementation
class WebSocketDataSourceImpl implements WebSocketDataSource {
  WebSocketDataSourceImpl({
    required this.storageService,
    required String url,
  }) : _url = url {
    LogUtils.d('WebSocketDataSource created for URL: $_url',
        tag: 'WebSocketDataSourceImpl.constructor');
  }

  @override
  String get url => _url;

  final String _url;
  final StorageService storageService;
  WebSocket? _socket;
  bool _isConnected = false;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  final _messageController =
      StreamController<response.WebsocketResponse>.broadcast();

  // Configuration
  static const int heartbeatInterval = 30; // seconds
  static const int initialReconnectDelay = 1; // seconds
  static const int maxReconnectDelay = 15; // seconds
  static String heartbeatMessage = jsonEncode({'msg': 'ping'});

  int _reconnectAttempts = 0;
  DateTime? _lastHeartbeatResponse;
  Future<void>? _activeReconnectTask; // 当前活跃的重连任务
  Future<ResultWithData<void>>? _activeConnectTask; // 当前活跃的连接任务

  // 添加连接状态锁，确保连接操作的原子性
  bool _isConnecting = false;

  @override
  bool get isConnected => _isConnected;

  @override
  Future<ResultWithData<void>> connect() async {
    // 如果已有活跃的连接任务，等待其完成
    if (_activeConnectTask != null) {
      LogUtils.d('连接任务已在进行中，等待完成 - $_url',
          tag: 'WebSocketDataSourceImpl.connect');
      return await _activeConnectTask!;
    }

    // 如果已经连接，直接返回成功
    if (_isConnected) {
      LogUtils.d('WebSocket已连接，无需重复连接 - $_url',
          tag: 'WebSocketDataSourceImpl.connect');
      return Either.right(null);
    }

    // 创建新的连接任务
    _activeConnectTask = _performConnect();

    try {
      return await _activeConnectTask!;
    } finally {
      _activeConnectTask = null;
    }
  }

  Future<ResultWithData<void>> _performConnect() async {
    final result = await _establishConnection(closeCode: CloseCode.initial);

    // 如果连接失败，启动自动重连机制
    result.fold(
      (error) {
        LogUtils.w(
          'Initial connection failed, starting auto-reconnect: ${error.message}',
          tag: 'WebSocketDataSourceImpl._performConnect',
        );
        // 直接调用重连，避免Future.microtask可能导致的竞态条件
        // 使用unawaited确保不阻塞当前调用
        unawaited(tryRefreshTokenAndReconnect());
      },
      (_) {
        // 连接成功，重置重连计数
        _reconnectAttempts = 0;
      },
    );

    return result;
  }

  Future<ResultWithData<void>> _establishConnection({
    required CloseCode closeCode,
  }) async {
    try {
      // 检查是否正在连接中，避免并发连接
      if (_isConnecting) {
        LogUtils.d('WebSocket正在连接中，跳过重复连接请求',
            tag: 'WebSocketDataSourceImpl._establishConnection');
        return Either.right(null);
      }

      // 如果已经连接，直接返回成功
      if (_isConnected) {
        LogUtils.d('WebSocket已连接，跳过连接',
            tag: 'WebSocketDataSourceImpl._establishConnection');
        return Either.right(null);
      }

      // 设置连接状态锁
      _isConnecting = true;

      try {
        // Close existing connection if any
        await disconnect(closeCode: closeCode);

        // 再次检查是否已经连接（可能在disconnect过程中其他任务已经连接）
        if (_isConnected) {
          LogUtils.d('WebSocket已在其他任务中连接，跳过连接',
              tag: 'WebSocketDataSourceImpl._establishConnection');
          return Either.right(null);
        }

        LogUtils.i(
          'Attempting to connect to WebSocket: $_url',
          tag: 'WebSocketDataSourceImpl._establishConnection',
        );

        final accessToken = storageService.accessToken;
        if (accessToken.isEmpty) {
          return Either.left(const AppException(
            statusCode: 401,
            message: 'No session info available',
            identifier: 'WS_NO_SESSION_INFO',
          ));
        }

        final timezone = storageService.getString(SPKeys.timezone);

        _socket = await WebSocket.connect(
          _url,
          headers: {
            'accept': 'application/json',
            'content-type': 'application/json',
            'GK-platform': getIt<IDeviceInfoService>().platform,
            'GK-app-version': getIt<IPackageInfoService>().version,
            'GK-Timezone': timezone,
            'Authorization': 'Bearer $accessToken',
          },
        );

        _socket!.listen(
          handleMessage,
          onError: handleError,
          onDone: handleDisconnection,
          cancelOnError: false,
        );

        _isConnected = true;
        _reconnectAttempts = 0;
        startHeartbeat();
        LogUtils.i('WebSocket connected successfully to: $_url',
            tag: 'WebSocketDataSourceImpl.connect');
        return Either.right(null);
      } finally {
        // 无论成功还是失败，都要释放连接状态锁
        _isConnecting = false;
      }
    } on WebSocketException catch (e) {
      _isConnected = false;
      _isConnecting = false; // 确保在异常情况下释放连接状态锁
      final error = AppException(
        statusCode: 1006,
        message: 'WebSocket connection failed: ${e.message}',
        identifier: 'WS_CONNECTION_ERROR',
      );
      LogUtils.e(
        'WebSocket connection error',
        error: error,
        tag: 'WebSocketDataSourceImpl.connect',
      );
      return Either.left(error);
    } catch (e) {
      _isConnected = false;
      _isConnecting = false; // 确保在异常情况下释放连接状态锁
      final error = AppException(
        statusCode: 500,
        message: 'Unexpected WebSocket error: $e',
        identifier: 'WS_UNEXPECTED_ERROR',
      );
      LogUtils.e(
        'WebSocket unexpected error',
        error: error,
        tag: 'WebSocketDataSourceImpl.connect',
      );
      return Either.left(error);
    }
  }

  void handleMessage(dynamic message) {
    if (jsonDecode(message)['msg'] == 'pong') {
      _lastHeartbeatResponse = DateTime.now();
      LogUtils.d(message, tag: 'heartBeat.$hashCode');
      return;
    }

    try {
      LogUtils.d('onMessage: ${message.toString()}',
          tag: 'WebSocketRepositoryImpl._setupMessageListener');
      final resp =
          response.WebsocketResponse.fromJson(jsonDecode(message ?? '{}'));

      _messageController.add(resp);
    } catch (e) {
      LogUtils.e(e.toString(),
          tag: 'WebSocketRepositoryImpl._setupMessageListener.error');
    }
  }

  void handleError(dynamic error) {
    final exception = error is AppException
        ? error
        : AppException(
            statusCode: 1006,
            message: error.toString(),
            identifier: 'WS_ERROR',
          );

    LogUtils.e('WebSocket error: ${exception.message}',
        tag: 'WebSocketDataSourceImpl._handleError');
    _isConnected = false;

    // 如果不是在dispose过程中，才添加错误到流中
    if (!isDisposing) {
      _messageController.addError(exception);
      // 尝试刷新token并重连
      tryRefreshTokenAndReconnect();
    }
  }

  void handleDisconnection() {
    LogUtils.i(
      'WebSocket connection closed: ${_socket?.closeCode} $url $hashCode}',
      tag: 'WebSocketDataSourceImpl._handleDisconnection',
    );

    // 如果正在dispose，不进行任何重连操作
    if (isDisposing) {
      LogUtils.d('WebSocket正在dispose，跳过重连逻辑',
          tag: 'WebSocketDataSourceImpl._handleDisconnection');
      return;
    }

    if ([
      CloseCode.initial.value,
      CloseCode.goingAway.value,
    ]
        .contains(_socket?.closeCode)) {
      stopHeartbeat();
      return;
    }

    _isConnected = false;

    // 对于所有非客户端主动断开的连接，都先尝试刷新token
    // 不再依赖closeCode判断是否需要刷新token
    tryRefreshTokenAndReconnect();
  }

  @override
  Future<void> tryRefreshTokenAndReconnect() async {
    // 如果正在dispose，不进行重连
    if (isDisposing) {
      LogUtils.d('WebSocket正在dispose，跳过重连',
          tag: 'WebSocketDataSourceImpl.tryRefreshTokenAndReconnect');
      return;
    }

    // 如果已有活跃的重连任务，等待其完成
    if (_activeReconnectTask != null) {
      LogUtils.d('重连任务已在进行中，等待完成',
          tag: 'WebSocketDataSourceImpl.tryRefreshTokenAndReconnect');
      await _activeReconnectTask;
      return;
    }

    // 创建新的重连任务
    _activeReconnectTask = performReconnect();

    try {
      await _activeReconnectTask;
    } finally {
      _activeReconnectTask = null;
    }
  }

  Future<void> performReconnect() async {
    // 如果正在dispose，不进行重连
    if (isDisposing) {
      LogUtils.d('WebSocket正在dispose，跳过token刷新和重连',
          tag: 'WebSocketDataSourceImpl._performReconnect');
      return;
    }

    final result = await getIt<TokenRefreshService>().refreshToken();
    result.fold((left) {
      LogUtils.e('token刷新失败', tag: 'WebSocketDataSourceImpl._performReconnect');
      // token刷新失败时，仍然尝试重连
      initiateReconnection();
    }, (right) {
      LogUtils.d('token刷新成功，正在重连...',
          tag: 'WebSocketDataSourceImpl._performReconnect');

      // 重置重连尝试计数，让重连立即执行
      _reconnectAttempts = 0;
      initiateReconnection();
    });
  }

  void initiateReconnection() {
    // 如果正在dispose，不进行重连
    if (isDisposing) {
      LogUtils.d('WebSocket正在dispose，跳过重连调度',
          tag: 'WebSocketDataSourceImpl._initiateReconnection');
      return;
    }

    _reconnectTimer?.cancel();

    // Calculate delay using exponential backoff
    final delay = min(
      initialReconnectDelay * pow(2, _reconnectAttempts),
      maxReconnectDelay,
    ).toInt();

    LogUtils.i(
      'Scheduling reconnection attempt ${_reconnectAttempts + 1} in $delay seconds',
      tag: 'WebSocketDataSourceImpl._initiateReconnection',
    );

    _reconnectTimer = Timer(Duration(seconds: delay), () async {
      // 在定时器回调中再次检查dispose状态
      if (isDisposing) {
        LogUtils.d('WebSocket正在dispose，取消重连尝试',
            tag: 'WebSocketDataSourceImpl._initiateReconnection');
        return;
      }

      _reconnectAttempts++;

      final result = await _establishConnection(
        closeCode: CloseCode.normal,
      );
      result.fold(
        (error) {
          LogUtils.e(
            'Reconnection attempt $_reconnectAttempts failed: ${error.message}',
            tag: 'WebSocketDataSourceImpl._initiateReconnection',
          );

          if (error.identifier == 'WS_NO_SESSION_INFO') {
            _reconnectTimer?.cancel();
            return;
          }

          initiateReconnection();
        },
        (_) {
          LogUtils.i(
            'Reconnection successful after $_reconnectAttempts attempts',
            tag: 'WebSocketDataSourceImpl._initiateReconnection',
          );
          _reconnectAttempts = 0;

          // 重连成功后启动心跳
          startHeartbeat();
        },
      );
    });
  }

  @override
  Future<ResultWithData<void>> disconnect(
      {required CloseCode closeCode}) async {
    try {
      stopHeartbeat();
      _reconnectTimer?.cancel();
      await _socket?.close(closeCode.value);

      LogUtils.i('WebSocket disconnected',
          tag: 'WebSocketDataSourceImpl.disconnect');

      _isConnected = false;
      _isConnecting = false; // 重置连接状态锁
      _reconnectAttempts = 0;
      return Either.right(null);
    } catch (e) {
      _isConnecting = false; // 确保在异常情况下也释放连接状态锁
      return Either.left(AppException(
        statusCode: 500,
        message: 'Failed to disconnect WebSocket: ${e.toString()}',
        identifier: 'WS_DISCONNECT_ERROR',
      ));
    }
  }

  @override
  Stream<response.WebsocketResponse> get messageStream {
    return _messageController.stream;
  }

  @override
  Future<ResultWithData<void>> sendMessage(
      WebSocketMessageEntity message) async {
    if (!_isConnected || _socket == null) {
      return Either.left(const AppException(
        statusCode: 1006,
        message: 'WebSocket is not connected',
        identifier: 'WS_NOT_CONNECTED',
      ));
    }

    try {
      _socket!.add(message.data);
      return Either.right(null);
    } catch (e) {
      LogUtils.e('Failed to send message: $e',
          tag: 'WebSocketDataSourceImpl.sendMessage');
      handleError(e);
      return Either.left(AppException(
        statusCode: 500,
        message: 'Failed to send message: ${e.toString()}',
        identifier: 'WS_SEND_ERROR',
      ));
    }
  }

  @override
  void startHeartbeat() {
    _heartbeatTimer?.cancel();
    _lastHeartbeatResponse = DateTime.now(); // Initialize last response time
    _heartbeatTimer = Timer.periodic(
      const Duration(seconds: heartbeatInterval),
      (_) => sendHeartbeat(),
    );

    // Immediately send a heartbeat to verify connection
    sendHeartbeat();
  }

  @override
  void stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  void sendHeartbeat() async {
    if (!_isConnected) return;

    try {
      // Check if we haven't received a response for too long
      if (_lastHeartbeatResponse != null) {
        final elapsed = DateTime.now().difference(_lastHeartbeatResponse!);
        if (elapsed.inSeconds > heartbeatInterval * 2) {
          LogUtils.w(
            '$url No heartbeat response received for ${elapsed.inSeconds} seconds, connection might be dead',
            tag: 'WebSocketDataSourceImpl._sendHeartbeat',
          );
          // Connection might be dead, initiate reconnection
          handleDisconnection();
          return;
        }
      }

      // Send heartbeat
      await sendMessage(WebSocketMessageEntity(
        data: heartbeatMessage,
        type: WebSocketMessageType.json,
      ));
    } catch (e) {
      LogUtils.e(
        'Failed to send heartbeat: $e',
        tag: 'WebSocketDataSourceImpl._sendHeartbeat',
      );
      handleError(e);
    }
  }

  @override
  void resetReconnectionState() {
    LogUtils.i(
      'Resetting reconnection state',
      tag: 'WebSocketDataSourceImpl.resetReconnectionState',
    );
    _reconnectAttempts = 0;
    _reconnectTimer?.cancel();

    // 注意：不直接设置为null，而是让正在进行的任务自然完成
    // 这样可以避免任务泄漏和竞态条件
    // _activeReconnectTask 和 _activeConnectTask 会在各自的finally块中被清理

    // 重置连接状态锁，允许新的连接尝试
    _isConnecting = false;
  }

  // 添加标志位来标识是否正在dispose
  bool isDisposing = false;

  /// 添加dispose方法，用于释放资源
  /// 应在使用此服务的对象被销毁时调用
  @override
  Future<void> dispose() async {
    // 设置dispose标志，防止重连
    isDisposing = true;

    stopHeartbeat();
    _reconnectTimer?.cancel();

    // 等待活跃的任务完成，避免任务泄漏
    if (_activeReconnectTask != null) {
      try {
        await _activeReconnectTask;
      } catch (e) {
        LogUtils.w('Error waiting for reconnect task to complete: $e',
            tag: 'WebSocketDataSourceImpl.dispose');
      }
    }

    if (_activeConnectTask != null) {
      try {
        await _activeConnectTask;
      } catch (e) {
        LogUtils.w('Error waiting for connect task to complete: $e',
            tag: 'WebSocketDataSourceImpl.dispose');
      }
    }

    // 清理任务引用和状态
    _activeReconnectTask = null;
    _activeConnectTask = null;
    _isConnecting = false;
    _isConnected = false;

    // 关闭消息流
    await _messageController.close();

    // 最后关闭WebSocket连接
    await _socket?.close(CloseCode.goingAway.value);
  }
}
